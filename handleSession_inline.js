import { connect } from 'cloudflare:sockets';

// 从原文件复制的全局配置
const globalControllerConfig = {
    connectMode: 'direct',
    retryMode: 'relayip',
    targetProtocolType0: '18c-_-1b0-_-1bc-_-1d4-_-190-_-198-_-1b0-_-184-_-1c8-_-194-_-e8-_-1d8-_-1b0-_-194-_-1cc-_-1cc',
    targetProtocolType1: '18c-_-1b0-_-1bc-_-1d4-_-190-_-198-_-1b0-_-184-_-1c8-_-194-_-e8-_-1d0-_-1c8-_-1bc-_-1a8-_-184-_-1b8',
    targetPathType0: 'vlws',
    targetPathType1: 'trws',
};

const globalSessionConfig = {
    connect: {
        connectMode: 'direct',
        retryMode: 'relayip',
    },
    user: {
        id: '49f37b98-c37f-4d46-be93-1fe0a742dd43',
        pass: 'a233255z',
        sha224: '419023a775279d21cdbda41971c0bb52e962f11b4f4bfba6015a268b',
    },
    relay: {
        ip: 'jp.almain126.changeip.biz',
        _port: null,
        get port() {
            if (this._port !== null) {
                return this._port;
            }
            return this.ip.includes(':') ? this.ip.split(':')[1] : (null || undefined);
        },
        set port(value) {
            this._port = value;
        },
        socks: 'web5.serv00.com:13668',
    },
};

// 辅助函数
function decodeBase64Url(encodedString) {
    return Uint8Array.from(atob(encodedString.replaceAll('-', '+').replaceAll('_', '/')), (c) => c.charCodeAt(0)).buffer;
}

function matchUuid(extractedID, uuidString) {
    uuidString = uuidString.replaceAll('-', '')
    for (let index = 0; index < 16; index++) {
        const expected = parseInt(uuidString.substring(index * 2, index * 2 + 2), 16)
        if (extractedID[index] !== expected) {
            return false
        }
    }
    return true
}

// 创建上游可读流（基于_worker.js的方式）
function createUpstreamReadable(server, earlyHeader) {
    const upstreamReadable = new ReadableStream({
        start(controller) {
            // 预处理early header
            if (earlyHeader) {
                try {
                    controller.enqueue(decodeBase64Url(earlyHeader));
                } catch (e) { }
            }

            // 处理WebSocket消息
            const handleMessage = (e) => {
                try {
                    controller.enqueue(e.data);
                } catch (e) {
                    // Ignore enqueue errors to prevent uncaught exceptions
                }
            };

            const handleClose = () => {
                try {
                    controller.close();
                } catch (e) {
                    // Ignore close errors
                }
            };

            const handleError = (event) => {
                try {
                    controller.error(event);
                    if (server && server.close instanceof Function) {
                        server.close(1013);
                    }
                } catch (e) {
                    // Ignore error propagation issues
                }
            };

            server.addEventListener("message", handleMessage);
            server.addEventListener("close", handleClose);
            server.addEventListener("error", handleError);
        },
    });

    return upstreamReadable;
}

// 通用地址解析函数
function parseAddress(bytes, view, decoder, offset, addressType) {
    let hostname = '';

    switch (addressType) {
        case 1: { // IPv4 (两种协议都是1)
            hostname = `${bytes[offset]}.${bytes[offset + 1]}.${bytes[offset + 2]}.${bytes[offset + 3]}`;
            offset += 4;
            break;
        }
        case 2: { // Domain name (Type0协议)
            const domainLen = bytes[offset++];
            hostname = decoder.decode(bytes.subarray(offset, offset + domainLen));
            offset += domainLen;
            break;
        }
        case 3: { // IPv6 (Type0协议) 或 Domain name (Type1协议)
            if (bytes[offset] < 16) { // 判断是域名长度还是IPv6数据
                // Domain name (Type1协议)
                const domainLen = bytes[offset++];
                hostname = decoder.decode(bytes.subarray(offset, offset + domainLen));
                offset += domainLen;
            } else {
                // IPv6 (Type0协议)
                hostname = view.getUint16(offset).toString(16);
                for (let i = 1; i < 8; i++) {
                    hostname += ':' + view.getUint16(offset + i * 2).toString(16);
                }
                offset += 16;
            }
            break;
        }
        case 4: { // IPv6 (Type1协议)
            hostname = view.getUint16(offset).toString(16);
            for (let i = 1; i < 8; i++) {
                hostname += ':' + view.getUint16(offset + i * 2).toString(16);
            }
            offset += 16;
            break;
        }
        case 38: { // IPv6 (Type0协议特殊情况)
            for (let i = 0; i < 8; i++) {
                hostname += (i ? ':' : '') + view.getUint16(offset + i * 2).toString(16);
            }
            offset += 16;
            break;
        }
        case 48: { // IPv6 (Type1协议特殊情况)
            for (let i = 0; i < 8; i++) {
                hostname += (i ? ':' : '') + view.getUint16(offset + i * 2).toString(16);
            }
            offset += 16;
            break;
        }
        default: {
            throw new Error(`Unsupported address type: ${addressType}`);
        }
    }

    return { hostname, offset };
}

// 内联协议头处理函数（基于原parseProtocolHeaderType0和parseProtocolHeaderType1）
function processProtocolHeader(buffer, protocolMode, wsInterface) {
    const bytes = new Uint8Array(buffer);
    const view = new DataView(buffer);
    const decoder = new TextDecoder();

    if (protocolMode === globalControllerConfig.targetProtocolType0) {
        // Type0 协议处理
        const extractedID = bytes.subarray(1, 17)
        if (!matchUuid(extractedID, globalSessionConfig.user.id)) {
            if (wsInterface && wsInterface.close instanceof Function) {
                wsInterface.close(1013, 'Invalid user');
            }
            throw new Error(`Invalid user: UUID does not match`);
        }

        const version = bytes[0];
        const optionsLength = bytes[17];
        const commandIndex = 18 + optionsLength;
        const command = bytes[commandIndex];

        const portIndex = 18 + optionsLength + 1;
        const port = view.getUint16(portIndex);

        let offset = portIndex + 2;
        const addressType = bytes[offset++];

        // 使用通用地址解析函数
        const addressResult = parseAddress(bytes, view, decoder, offset, addressType);
        const hostname = addressResult.hostname;
        offset = addressResult.offset;

        const rawClientData = bytes.subarray(offset);

        return {
            hasError: false,
            addressType: addressType,
            addressRemote: hostname,
            portRemote: port,
            rawDataIndex: offset,
            rawClientData
        };

    } else if (protocolMode === globalControllerConfig.targetProtocolType1) {
        // Type1 协议处理
        const crLfIndex = 56;
        const extractedPassword = decoder.decode(bytes.subarray(0, crLfIndex));
        if (extractedPassword !== globalSessionConfig.user.sha224) {
            if (wsInterface && wsInterface.close instanceof Function) {
                wsInterface.close(1013, 'Invalid password');
            }
            throw new Error(`Invalid password`)
        }

        let offset = crLfIndex + 2;
        const command = bytes[offset++];
        const addressType = bytes[offset++];

        // 使用通用地址解析函数
        const addressResult = parseAddress(bytes, view, decoder, offset, addressType);
        const hostname = addressResult.hostname;
        offset = addressResult.offset;

        const port = view.getUint16(offset);
        offset += 4;

        const rawClientData = bytes.subarray(offset);

        return {
            hasError: false,
            addressType: addressType,
            addressRemote: hostname,
            portRemote: port,
            rawDataIndex: offset,
            rawClientData
        };
    } else {
        return {
            hasError: true,
            message: `Unknown protocol mode: ${protocolMode}`
        };
    }
}

// SOCKS5地址解析器
function socks5AddressParser(address) {
    let [userInfo, hostInfo] = address.includes('@') ? address.split('@') : ['', address];
    let username = '', password = '';

    if (userInfo) {
        [username, password] = userInfo.includes(':') ? userInfo.split(':') : [userInfo, ''];
    }

    const [hostname, port] = hostInfo.includes(':') ?
        [hostInfo.substring(0, hostInfo.lastIndexOf(':')), hostInfo.substring(hostInfo.lastIndexOf(':') + 1)] :
        [hostInfo, '1080'];

    return { username, password, hostname, port: parseInt(port) };
}

// SOCKS5连接函数（简化版）
async function socks5Connect(addressType, addressRemote, portRemote, targetProtocol) {
    const { username, password, hostname, port } = socks5AddressParser(globalSessionConfig.relay.socks);
    let socket, reader, writer;
    const encoder = new TextEncoder()

    try {
        // Connect to the SOCKS server
        socket = await connect({ hostname, port });
        reader = socket.readable.getReader();
        writer = socket.writable.getWriter();
        if (!reader || !writer) throw new Error('Failed to get reader/writer');

        // Send SOCKS5 greeting
        const socksGreeting = new Uint8Array([5, 2, 0, 2]); // Support No Auth and Username/Password Auth
        await writer.write(socksGreeting);

        // Read server response
        let res = (await reader.read()).value;
        if (res[0] !== 0x05) throw new Error('Invalid SOCKS version');

        // Handle authentication
        if (res[1] === 0x02) { // Username/Password authentication required
            if (!username || !password) throw new Error('Authentication required but no credentials provided');

            const authRequest = new Uint8Array([1, username.length, ...encoder.encode(username), password.length, ...encoder.encode(password)]);
            await writer.write(authRequest);

            res = (await reader.read()).value;
            if (res[0] !== 0x01 || res[1] !== 0x00) throw new Error('Authentication failed');
        }

        // Build connection request
        let DSTADDR;
        switch (addressType) {
            case 1: // IPv4
                DSTADDR = new Uint8Array([1, ...addressRemote.split('.').map(Number)]);
                break;
            case 2: // Domain name
                DSTADDR = new Uint8Array([3, addressRemote.length, ...encoder.encode(addressRemote)]);
                break;
            case 3: // IPv6
                DSTADDR = new Uint8Array([4, ...addressRemote.split(':').flatMap(part => {
                    const num = parseInt(part, 16);
                    return [num >> 8, num & 0xff];
                })]);
                break;
            default:
                throw new Error(`Unsupported address type: ${addressType}`);
        }

        const socksRequest = new Uint8Array([5, 1, 0, ...DSTADDR, portRemote >> 8, portRemote & 0xff]);
        await writer.write(socksRequest);

        // Read final response
        res = (await reader.read()).value;
        if (res[1] !== 0x00) throw new Error('Connection failed');

        reader.releaseLock();
        writer.releaseLock();
        return socket;

    } catch (error) {
        if (reader) reader.releaseLock();
        if (writer) writer.releaseLock();
        if (socket) {
            try { await socket.close(); } catch (closeError) { }
        }
        throw error;
    }
}

// 创建连接函数
async function createConnection(header, mode, protocolMode) {
    const { addressType, addressRemote, portRemote } = header;
    const useTargetProtocol = protocolMode === globalControllerConfig.targetProtocolType0;

    switch (mode) {
        case 'relayip': {
            const needDirect =
                [1].includes(addressType) ||
                (useTargetProtocol && [3].includes(addressType)) ||
                (!useTargetProtocol && [4].includes(addressType));
            return needDirect
                ? connect({ hostname: addressRemote, port: portRemote })
                : connect({
                    hostname: globalSessionConfig.relay.ip,
                    port: globalSessionConfig.relay.port || portRemote,
                });
        }
        case 'relaysocks': {
            return socks5Connect(addressType, addressRemote, portRemote, useTargetProtocol);
        }
        case 'direct': {
            return connect({ hostname: addressRemote, port: portRemote });
        }
        default:
            return connect({ hostname: addressRemote, port: portRemote });
    }
}

// 单次拨号包装器
async function dial(header, mode, protocolMode) {
    const iface = await createConnection(header, mode, protocolMode);
    await iface.opened; // Return after the connection is fully established
    return iface;
}

// 主要的handleSession函数 - 内联处理版本
export async function handleSession(request, env, ctx, protocolMode) {
    const { 0: client, 1: server } = Object.values(new WebSocketPair());
    server.accept();

    const earlyHeader = request.headers.get("sec-websocket-protocol") || "";

    // 创建上游可读流
    const upstreamReadable = createUpstreamReadable(server, earlyHeader);

    let tcpInterface;
    let writer;

    (async () => {
        try {
            // 内联处理协议头部解析
            let header;
            try {
                // 直接从流中读取第一个chunk进行头部解析
                const reader = upstreamReadable.getReader();
                const { value: chunk } = await reader.read();
                reader.releaseLock();

                if (!chunk) {
                    throw new Error('Client closed before sending header');
                }

                // 内联处理协议头部
                const headerResult = processProtocolHeader(chunk, protocolMode, server);

                if (headerResult.hasError) {
                    throw new Error(headerResult.message);
                }

                header = headerResult;

            } catch (headerError) {
                server.close(1002, 'Header parse failed');
                return;
            }

            // 顺序拨号：connectMode → retryMode
            try {
                tcpInterface = await dial(header, globalControllerConfig.connectMode, protocolMode);
            } catch {
                try {
                    tcpInterface = await dial(header, globalControllerConfig.retryMode, protocolMode);
                } catch (e) {
                    throw e;
                }
            }

            writer = tcpInterface.writable.getWriter();

            // 写入剩余的客户端数据
            if (header.rawClientData && header.rawClientData.length > 0) {
                await writer.write(header.rawClientData);
            }

            // 上游数据传输：WebSocket → TCP
            upstreamReadable.pipeTo(tcpInterface.writable).catch((error) => {
                console.error('Upstream pipe error:', error);
            });

            // 下游数据传输：TCP → WebSocket
            tcpInterface.readable.pipeTo(new WritableStream({
                async write(chunk) {
                    try {
                        server.send(chunk);
                    } catch (e) {
                        if (e instanceof TypeError) return; // WebSocket已关闭
                        throw e;
                    }
                }
            })).catch((error) => {
                console.error('Downstream pipe error:', error);
            });

        } catch (error) {
            console.error('Session error:', error);
            if (writer) {
                try {
                    writer.releaseLock();
                } catch (e) { }
            }
            if (tcpInterface) {
                try {
                    tcpInterface.close();
                } catch (e) { }
            }
            server.close(1002, 'Session failed');
        }
    })();

    return new Response(null, {
        status: 101,
        webSocket: client,
    });
}


